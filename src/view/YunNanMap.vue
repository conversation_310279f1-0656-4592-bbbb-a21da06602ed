<template>
  <div class="yunnan-map-container">
    <!-- 地图容器 -->
    <div id="yunnan-map" class="map-container"></div>

    <!-- 地图控制台 -->
    <MapControlPanel
      @mapModeChange="handleMapModeChange"
      @mapLayerChange="handleMapLayerChange"
      @filterChange="handleFilterChange"
      @facilityFilterChange="handleFacilityFilterChange"
      @voltageFilterChange="handleVoltageFilterChange"
      @placeNamesChange="handlePlaceNamesChange"
      @boundariesChange="handleBoundariesChange"
      @levelFilterChange="handleLevelFilterChange"
    />

    <!-- 悬停提示框 -->
    <HoverTooltip
      :visible="hoverTooltipVisible"
      :data="hoverTooltipData"
      :position="hoverTooltipPosition"
    />

    <!-- 详细信息面板 -->
    <DetailPanel
      :visible="detailPanelVisible"
      :data="detailPanelData"
      :type="detailPanelType"
      @close="handleDetailPanelClose"
    />

  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { createMap, addGeoJsonLayer, switchMapLayer } from '../data/map.js'
import yunnanGeoJson from '../assets/map/yunnan.json'
import MapControlPanel from '../components/MapControlPanel.vue'
import HoverTooltip from '../components/HoverTooltip.vue'
import DetailPanel from '../components/DetailPanel.vue'

// 导入管理器 - 照搬1.4版本导入方式
import { PowerLineRenderer } from '../managers/PowerLineRenderer.js'
import MassMarkerManager from '../managers/MassMarkerManager.js'
import { InteractionManager } from '../managers/InteractionManager.js'
import PlaceNameManager from '../managers/PlaceNameManager.js'

import PowerLineManager from '../PowerLineManager.js'

// 导入数据
import { powerLinesData, powerFacilitiesData } from '../data/index.js'

// 响应式数据
const loading = ref(true)
const error = ref('')
const mapInstance = ref(null)
const AMapInstance = ref(null)
const geoJsonLayer = ref(null)
const currentMapLayer = ref('normal') // 当前地图图层类型
const satelliteLayer = ref(null) // 卫星图层实例
const roadNetLayer = ref(null) // 路网图层实例

// 管理器实例
const powerLineManager = ref(null)
const powerLineRenderer = ref(null)
const massMarkerManager = ref(null)
const interactionManager = ref(null)
const placeNameManager = ref(null)


// 交互UI状态
const hoverTooltipVisible = ref(false)
const hoverTooltipData = ref(null)
const hoverTooltipPosition = ref(null)
const detailPanelVisible = ref(false)
const detailPanelData = ref(null)
const detailPanelType = ref(null)

/**
 * 初始化地图
 */
const initMap = async () => {
  try {
    loading.value = true
    error.value = ''

    // 检查容器元素是否存在
    const container = document.getElementById('yunnan-map')
    if (!container) {
      throw new Error('地图容器元素未找到')
    }

    console.log('地图容器尺寸:', container.offsetWidth, 'x', container.offsetHeight)

    // 创建地图实例
    const { AMap, map } = await createMap('yunnan-map', {
      zoom: 6, // 适合云南省的缩放级别
      center: [101.5, 25.0], // 云南省中心坐标
      zooms: [3, 15] // 缩放范围限制
    })

    // 保存实例引用
    mapInstance.value = map
    AMapInstance.value = AMap

    console.log('地图实例创建成功:', map)

    // 强制重新计算地图尺寸和触发resize事件
    setTimeout(() => {
      // 强制设置地图容器尺寸
      const container = document.getElementById('yunnan-map')
      if (container) {
        container.style.width = '100vw'
        container.style.height = '100vh'
      }

      // 调用高德地图的resize方法
      if (map.getSize) {
        map.getSize()
      }

      // 重新设置中心点
      map.setCenter([101.5, 25.0])

      // 触发resize事件确保地图正确显示
      window.dispatchEvent(new Event('resize'))

      console.log('地图尺寸已强制更新')
    }, 500)

    // 添加云南省边界
    await addYunnanBoundary(map, AMap)

    // 添加地图控件
    addMapControls(map, AMap)

    // 初始化电力系统管理器
    await initPowerSystemManagers(map, AMap)

    loading.value = false
    console.log('地图初始化成功')

  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = err.message || '地图加载失败，请检查网络连接'
    loading.value = false
  }
}

/**
 * 添加云南省边界
 */
const addYunnanBoundary = async (map, AMap) => {
  try {
    // 自定义边界样式
    const boundaryStyle = {
      strokeColor: '#FF6B35', // 橙红色边界
      strokeWeight: 3, // 边界宽度
      strokeOpacity: 0.9, // 边界透明度
      fillColor: '#4CAF50', // 绿色填充
      fillOpacity: 0.15, // 填充透明度
      strokeStyle: 'solid' // 实线边界
    }

    // 添加GeoJSON图层
    const layer = addGeoJsonLayer(map, AMap, yunnanGeoJson, boundaryStyle)
    geoJsonLayer.value = layer

    console.log('云南省边界添加成功')

  } catch (err) {
    console.error('云南省边界添加失败:', err)
    throw new Error('边界数据加载失败')
  }
}

/**
 * 添加地图控件
 */
const addMapControls = (map, AMap) => {
  try {
    // 添加缩放控件
    const toolbar = new AMap.ToolBar({
      position: 'RB', // 右下角
      ruler: false, // 不显示标尺
      noIpLocate: true, // 不显示定位按钮
      locate: false, // 不显示定位按钮
      liteStyle: true, // 精简模式
      direction: false // 不显示方向控件
    })
    map.addControl(toolbar)

    // 添加比例尺
    const scale = new AMap.Scale({
      position: 'LB' // 左下角
    })
    map.addControl(scale)

    console.log('地图控件添加成功')

  } catch (err) {
    console.error('地图控件添加失败:', err)
  }
}

/**
 * 初始化电力系统管理器 - 照搬1.4版本实现
 */
const initPowerSystemManagers = async (map, AMap) => {
  try {
    console.log('开始初始化电力系统管理器... (照搬1.4版本实现)')

    // 清理旧的管理器实例
    cleanupPowerSystemManagers()

    // 1. 初始化数据管理器
    powerLineManager.value = new PowerLineManager(map, AMap)

    // 2. 初始化线路渲染器 - 照搬1.4版本
    powerLineRenderer.value = new PowerLineRenderer(map, AMap)

    // 3. 初始化海量点管理器
    massMarkerManager.value = new MassMarkerManager(map, AMap)
    await massMarkerManager.value.initMassMarkers()

    // 暴露到全局作用域以便调试和测试
    window.massMarkerManager = massMarkerManager.value

    // 4. 初始化交互管理器 - 照搬1.4版本
    interactionManager.value = new InteractionManager(map, AMap)

    // 暴露到全局作用域以便调试和测试
    window.interactionManager = interactionManager.value

    // 5. 初始化地名管理器
    placeNameManager.value = new PlaceNameManager(map, AMap)

    // 6. 设置管理器之间的关联 - 照搬1.4版本交互逻辑
    setupManagerInteractions()

    // 7. 添加缩放级别监听
    setupZoomLevelListener(map, AMap)

    // 8. 修复3D模式右键拖拽bug
    fix3DMouseInteractionBug(map, AMap)

    // 9. 加载和渲染数据
    await loadAndRenderPowerData()

    console.log('电力系统管理器初始化完成 (照搬1.4版本实现)')

  } catch (error) {
    console.error('电力系统管理器初始化失败:', error)
    throw error
  }
}

/**
 * 清理电力系统管理器
 */
const cleanupPowerSystemManagers = () => {
  try {
    console.log('开始清理旧的电力系统管理器...')

    // 清理线路渲染器
    if (powerLineRenderer.value) {
      if (typeof powerLineRenderer.value.destroy === 'function') {
        powerLineRenderer.value.destroy()
      } else {
        powerLineRenderer.value.clearAllLines()
      }
      powerLineRenderer.value = null
    }

    // 清理海量点管理器
    if (massMarkerManager.value) {
      if (typeof massMarkerManager.value.destroy === 'function') {
        massMarkerManager.value.destroy()
      }
      massMarkerManager.value = null
    }

    // 3D模型管理器已移除

    // 清理交互管理器
    if (interactionManager.value) {
      if (typeof interactionManager.value.destroy === 'function') {
        interactionManager.value.destroy()
      }
      interactionManager.value = null
    }

    // 清理地名管理器
    if (placeNameManager.value) {
      if (typeof placeNameManager.value.destroy === 'function') {
        placeNameManager.value.destroy()
      }
      placeNameManager.value = null
    }

    // 清理数据管理器
    if (powerLineManager.value) {
      if (typeof powerLineManager.value.clear === 'function') {
        powerLineManager.value.clear()
      }
      powerLineManager.value = null
    }

    console.log('电力系统管理器清理完成')

  } catch (error) {
    console.error('电力系统管理器清理失败:', error)
  }
}

/**
 * 设置管理器之间的交互
 */
const setupManagerInteractions = () => {
  // 设置交互管理器
  interactionManager.value.initInteractions(
    powerLineRenderer.value,
    massMarkerManager.value
  )

  // 监听交互事件
  interactionManager.value.on('showHoverTooltip', (data) => {
    hoverTooltipVisible.value = data.visible
    hoverTooltipData.value = data.data
    hoverTooltipPosition.value = data.position
  })

  interactionManager.value.on('hideHoverTooltip', () => {
    hoverTooltipVisible.value = false
    hoverTooltipData.value = null
    hoverTooltipPosition.value = null
  })

  interactionManager.value.on('showDetailPanel', (data) => {
    detailPanelVisible.value = data.visible
    detailPanelData.value = data.data
    detailPanelType.value = data.type
  })

  interactionManager.value.on('hideDetailPanel', () => {
    detailPanelVisible.value = false
    detailPanelData.value = null
    detailPanelType.value = null
  })
}

/**
 * 加载和渲染电力数据
 */
const loadAndRenderPowerData = async () => {
  try {
    console.log('开始加载电力数据...')

    // 等待数据管理器加载完成
    if (!powerLineManager.value.isDataLoaded()) {
      await new Promise((resolve) => {
        const checkLoaded = () => {
          if (powerLineManager.value.isDataLoaded()) {
            resolve()
          } else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
      })
    }

    // 获取线路数据
    const linesData = powerLineManager.value.getAllLines()
    console.log('线路数据:', linesData)
    console.log('线路数据数量:', Object.keys(linesData).length)

    // 渲染线路
    await powerLineRenderer.value.renderAllLines(linesData)
    console.log('线路渲染完成')

    // 检查设施数据
    console.log('变电站数据:', powerFacilitiesData.substations)
    console.log('发电站数据:', powerFacilitiesData.powerPlants)
    console.log('变电站数量:', powerFacilitiesData.substations?.length || 0)
    console.log('发电站数量:', powerFacilitiesData.powerPlants?.length || 0)

    // 更新海量点数据
    console.log('开始更新电塔标记...')
    massMarkerManager.value.updateTowerMarkers(linesData)

    console.log('开始更新变电站标记...')
    massMarkerManager.value.updateSubstationMarkers(powerFacilitiesData.substations)

    console.log('开始更新发电站标记...')
    massMarkerManager.value.updatePowerPlantMarkers(powerFacilitiesData.powerPlants)

    // 3D模型功能已移除

    console.log('电力数据加载和渲染完成')

  } catch (error) {
    console.error('电力数据加载失败:', error)
    console.error('错误详情:', error.stack)
    throw error
  }
}

/**
 * 销毁地图实例
 */
const destroyMap = () => {
  try {
    // 清理电力系统管理器
    cleanupPowerSystemManagers()

    // 销毁地图实例
    if (mapInstance.value) {
      // 清理3D鼠标交互事件监听器
      if (mapInstance.value._mouseInteractionCleanup) {
        mapInstance.value._mouseInteractionCleanup()
        console.log('3D鼠标交互事件监听器已清理')
      }

      mapInstance.value.destroy()
      mapInstance.value = null
      AMapInstance.value = null
      geoJsonLayer.value = null
      console.log('地图实例已销毁')
    }

    // 重置UI状态
    hoverTooltipVisible.value = false
    hoverTooltipData.value = null
    hoverTooltipPosition.value = null
    detailPanelVisible.value = false
    detailPanelData.value = null
    detailPanelType.value = null

  } catch (err) {
    console.error('地图销毁失败:', err)
  }
}

/**
 * 处理地图模式切换
 */
const handleMapModeChange = async (mode) => {
  try {
    if (!mapInstance.value || !AMapInstance.value) {
      console.warn('地图实例未初始化，无法切换模式')
      return false
    }

    console.log('开始切换地图模式:', mode)

    if (mode === '3D') {
      // 切换到3D模式
      await switchTo3DMode()
    } else {
      // 切换到2D模式
      await switchTo2DMode()
    }

    console.log(`地图模式已成功切换到: ${mode}`)
    return true

  } catch (err) {
    console.error('地图模式切换失败:', err)
    return false
  }
}

/**
 * 切换到3D模式
 */
const switchTo3DMode = async () => {
  try {
    console.log('开始切换到3D模式...')

    // 重新创建地图实例以支持3D模式
    const container = document.getElementById('yunnan-map')
    if (!container) {
      throw new Error('地图容器未找到')
    }

    // 获取当前地图状态
    const currentCenter = mapInstance.value.getCenter()
    const currentZoom = mapInstance.value.getZoom()

    // 销毁当前地图实例
    mapInstance.value.destroy()

    // 创建支持3D的新地图实例
    const { AMap, map } = await createMap('yunnan-map', {
      zoom: Math.max(currentZoom, 8), // 3D模式下最小缩放级别
      center: [currentCenter.lng, currentCenter.lat],
      viewMode: '3D', // 设置为3D模式
      pitch: 45, // 设置倾斜角度
      rotation: 0, // 设置旋转角度
      rotateEnable: true, // 启用旋转
      pitchEnable: true, // 启用倾斜
      zooms: [3, 15] // 3D模式缩放范围
    })

    // 更新实例引用
    mapInstance.value = map
    AMapInstance.value = AMap

    // 重新添加云南省边界
    await addYunnanBoundary(map, AMap)

    // 重新添加地图控件
    addMapControls(map, AMap)

    // 恢复当前图层设置
    if (currentMapLayer.value !== 'normal') {
      const result = await switchMapLayer(map, AMap, currentMapLayer.value)
      if (result.success) {
        satelliteLayer.value = result.satelliteLayer
        roadNetLayer.value = result.roadNetLayer
      }
    }

    // 重新初始化电力系统管理器
    await initPowerSystemManagers(map, AMap)

    // 3D模型功能已移除，3D模式仅保留地图3D视角

    console.log('3D模式配置完成')

  } catch (error) {
    console.error('3D模式切换失败:', error)
    throw error
  }
}

/**
 * 切换到2D模式
 */
const switchTo2DMode = async () => {
  try {
    console.log('开始切换到2D模式...')

    // 重新创建地图实例以支持2D模式
    const container = document.getElementById('yunnan-map')
    if (!container) {
      throw new Error('地图容器未找到')
    }

    // 获取当前地图状态
    const currentCenter = mapInstance.value.getCenter()
    const currentZoom = mapInstance.value.getZoom()

    // 销毁当前地图实例
    mapInstance.value.destroy()

    // 创建2D地图实例
    const { AMap, map } = await createMap('yunnan-map', {
      zoom: currentZoom,
      center: [currentCenter.lng, currentCenter.lat],
      viewMode: '2D', // 设置为2D模式
      pitch: 0, // 重置倾斜角度
      rotation: 0, // 重置旋转角度
      rotateEnable: false, // 禁用旋转
      pitchEnable: false, // 禁用倾斜
      zooms: [3, 18] // 2D模式缩放范围
    })

    // 更新实例引用
    mapInstance.value = map
    AMapInstance.value = AMap

    // 重新添加云南省边界
    await addYunnanBoundary(map, AMap)

    // 重新添加地图控件
    addMapControls(map, AMap)

    // 恢复当前图层设置
    if (currentMapLayer.value !== 'normal') {
      const result = await switchMapLayer(map, AMap, currentMapLayer.value)
      if (result.success) {
        satelliteLayer.value = result.satelliteLayer
        roadNetLayer.value = result.roadNetLayer
      }
    }

    // 重新初始化电力系统管理器
    await initPowerSystemManagers(map, AMap)

    // 3D模型功能已移除

    console.log('2D模式配置完成')

  } catch (error) {
    console.error('2D模式切换失败:', error)
    throw error
  }
}

/**
 * 处理地图图层切换
 */
const handleMapLayerChange = async (layerType) => {
  try {
    if (!mapInstance.value || !AMapInstance.value) {
      console.warn('地图实例未初始化，无法切换图层')
      return false
    }

    console.log('开始切换地图图层:', layerType)

    // 切换地图图层
    const result = await switchMapLayer(mapInstance.value, AMapInstance.value, layerType)

    if (result.success) {
      currentMapLayer.value = layerType
      satelliteLayer.value = result.satelliteLayer
      roadNetLayer.value = result.roadNetLayer
      console.log(`地图图层已成功切换到: ${layerType}`)
      return true
    } else {
      console.error('地图图层切换失败:', result.error)
      return false
    }

  } catch (err) {
    console.error('地图图层切换失败:', err)
    return false
  }
}

/**
 * 处理线路筛选变更
 */
const handleFilterChange = async (filters) => {
  console.log('线路状态筛选条件变更:', filters)

  try {
    if (powerLineRenderer.value && powerLineManager.value) {
      // 获取线路数据
      const linesData = powerLineManager.value.getAllLines()

      // 应用筛选条件重新渲染线路
      await powerLineRenderer.value.applyFilters(filters, linesData)

      // 更新海量点数据（电塔位置可能因筛选而变化）
      if (massMarkerManager.value) {
        // 获取当前电压等级筛选条件
        const currentVoltageFilters = powerLineRenderer.value.activeVoltageFilters
        const filteredLinesData = powerLineManager.value.getCombinedFilteredLines(filters, currentVoltageFilters)
        massMarkerManager.value.updateTowerMarkers(filteredLinesData)

        // 3D模型功能已移除
      }

      console.log('状态筛选条件应用成功')
    }
  } catch (error) {
    console.error('应用状态筛选条件失败:', error)
  }
}

/**
 * 处理设施筛选变更
 */
const handleFacilityFilterChange = async (filterData) => {
  console.log('设施筛选条件变更:', filterData)

  try {
    if (massMarkerManager.value) {
      const { type, filters } = filterData

      if (type === 'substations') {
        // 应用变电站筛选
        await massMarkerManager.value.applySubstationFilters(filters)
        console.log('变电站筛选应用成功:', filters)
      } else if (type === 'powerPlants') {
        // 应用发电站筛选
        await massMarkerManager.value.applyPowerPlantFilters(filters)
        console.log('发电站筛选应用成功:', filters)
      } else if (type === 'towers') {
        // 应用电塔筛选
        await massMarkerManager.value.applyTowerFilters(filters)
        console.log('电塔筛选应用成功:', filters)
      }
    }
  } catch (error) {
    console.error('应用设施筛选条件失败:', error)
  }
}

/**
 * 处理电压等级筛选变更
 */
const handleVoltageFilterChange = async (filters) => {
  console.log('电压等级筛选条件变更:', filters)

  try {
    if (powerLineRenderer.value && powerLineManager.value) {
      // 获取线路数据
      const linesData = powerLineManager.value.getAllLines()

      // 应用电压等级筛选条件重新渲染线路
      await powerLineRenderer.value.applyVoltageFilters(filters, linesData)

      // 更新海量点数据（电塔位置可能因筛选而变化）
      if (massMarkerManager.value) {
        // 获取当前状态筛选条件
        const currentStatusFilters = powerLineRenderer.value.activeFilters
        const filteredLinesData = powerLineManager.value.getCombinedFilteredLines(currentStatusFilters, filters)
        massMarkerManager.value.updateTowerMarkers(filteredLinesData)

        // 3D模型功能已移除
      }

      console.log('电压等级筛选条件应用成功')
    }
  } catch (error) {
    console.error('应用电压等级筛选条件失败:', error)
  }
}

/**
 * 处理详细信息面板关闭
 */
const handleDetailPanelClose = () => {
  if (interactionManager.value) {
    interactionManager.value.hideDetailPanel()
  }
}

/**
 * 处理地名显示变更
 */
const handlePlaceNamesChange = (show) => {
  console.log('GeoJSON地名显示变更:', show)

  try {
    if (placeNameManager.value) {
      if (show) {
        placeNameManager.value.show()
      } else {
        placeNameManager.value.hide()
      }

      console.log(`GeoJSON地名标签已${show ? '显示' : '隐藏'}`)

      // 输出统计信息
      const stats = placeNameManager.value.getStats()
      console.log('地名管理器状态:', stats)
    } else {
      console.warn('地名管理器未初始化')
    }
  } catch (error) {
    console.error('地名显示切换失败:', error)
  }
}

/**
 * 处理行政边界显示变更
 */
const handleBoundariesChange = (show) => {
  console.log('行政边界显示变更:', show)

  try {
    if (mapInstance.value && geoJsonLayer.value) {
      if (show) {
        // 显示行政边界：将图层添加到地图
        if (!mapInstance.value.getLayers().includes(geoJsonLayer.value)) {
          mapInstance.value.add(geoJsonLayer.value)
        }
      } else {
        // 隐藏行政边界：从地图中移除图层
        mapInstance.value.remove(geoJsonLayer.value)
      }

      console.log(`行政边界显示已${show ? '开启' : '关闭'}`)
    }
  } catch (error) {
    console.error('行政边界显示切换失败:', error)
  }
}

/**
 * 处理线路级别筛选变更
 */
const handleLevelFilterChange = async (filters) => {
  console.log('线路级别筛选条件变更:', filters)

  try {
    if (powerLineRenderer.value && powerLineManager.value) {
      // 获取线路数据
      const linesData = powerLineManager.value.getAllLines()

      // 应用线路级别筛选条件重新渲染线路
      await powerLineRenderer.value.applyLevelFilters(filters, linesData)

      // 更新海量点数据（电塔位置可能因筛选而变化）
      if (massMarkerManager.value) {
        // 获取当前状态和电压等级筛选条件
        const currentStatusFilters = powerLineRenderer.value.activeFilters
        const currentVoltageFilters = powerLineRenderer.value.activeVoltageFilters
        const filteredLinesData = powerLineManager.value.getCombinedFilteredLines(
          currentStatusFilters,
          currentVoltageFilters,
          filters
        )
        massMarkerManager.value.updateTowerMarkers(filteredLinesData)

        // 3D模型功能已移除
      }

      console.log('线路级别筛选条件应用成功')
    }
  } catch (error) {
    console.error('应用线路级别筛选条件失败:', error)
  }
}

/**
 * 设置缩放级别监听器
 * 根据缩放级别和视图模式控制2D标记和3D模型的显示
 */
const setupZoomLevelListener = (map, AMap) => {
  try {
    console.log('设置缩放级别监听器...')

    // 监听缩放变化事件
    map.on('zoomchange', () => {
      const currentZoom = map.getZoom()
      const viewMode = map.getViewMode ? map.getViewMode() : (map.getViewMode_ ? map.getViewMode_() : '2D')

      console.log(`缩放级别变化: ${currentZoom}, 视图模式: ${viewMode}`)

      // 3D模型管理器会自动处理缩放变化（已有handleZoomChange方法）
      // 注意：不在这里直接调用massMarkerManager.controlTowerMarkersVisibility
      // 因为3D模型管理器会在模型加载完成后通知海量点管理器
    })

    // 监听视图模式变化（如果支持）
    if (map.on && typeof map.on === 'function') {
      map.on('viewchange', () => {
        const currentZoom = map.getZoom()
        const viewMode = map.getViewMode ? map.getViewMode() : (map.getViewMode_ ? map.getViewMode_() : '2D')

        console.log(`视图模式变化: ${viewMode}, 缩放级别: ${currentZoom}`)

        // 3D模型管理器会自动处理视图模式变化
        // 注意：不在这里直接调用massMarkerManager.controlTowerMarkersVisibility
        // 因为3D模型管理器会在模型加载完成后通知海量点管理器
      })
    }

    console.log('缩放级别监听器设置完成')
  } catch (error) {
    console.error('设置缩放级别监听器失败:', error)
  }
}

/**
 * 修复3D模式下右键拖拽后松开鼠标仍处于调整状态的bug
 * 新方案：使用高德地图原生事件系统
 */
const fix3DMouseInteractionBug = (map, AMap) => {
  try {
    console.log('开始修复3D鼠标交互bug...')

    // 检查是否为3D模式
    const viewMode = map.getViewMode_()
    if (viewMode !== '3D') {
      console.log('当前不是3D模式，跳过修复')
      return
    }

    // 状态跟踪
    let rightMousePressed = false
    let dragStarted = false
    let resetTimer = null

    // 监听地图的原生鼠标事件
    const handleMapMouseDown = (e) => {
      if (e.originEvent && e.originEvent.button === 2) {
        rightMousePressed = true
        dragStarted = false
        console.log('3D模式：右键按下（地图事件）')
      }
    }

    const handleMapMouseUp = (e) => {
      if (e.originEvent && e.originEvent.button === 2 && rightMousePressed) {
        rightMousePressed = false
        console.log('3D模式：右键松开（地图事件）')

        // 延迟重置状态，确保地图操作完成
        if (resetTimer) {
          clearTimeout(resetTimer)
        }

        resetTimer = setTimeout(() => {
          forceResetMapState()
        }, 150)
      }
    }

    // 监听拖拽开始事件
    const handleDragStart = () => {
      if (rightMousePressed) {
        dragStarted = true
        console.log('3D模式：右键拖拽开始')
      }
    }

    // 监听拖拽结束事件
    const handleDragEnd = () => {
      if (dragStarted) {
        dragStarted = false
        console.log('3D模式：右键拖拽结束')

        // 拖拽结束后立即重置状态
        setTimeout(() => {
          forceResetMapState()
        }, 100)
      }
    }

    // 强制重置地图状态
    const forceResetMapState = () => {
      try {
        console.log('3D模式：强制重置地图状态')

        // 直接设置地图状态，不检查当前状态
        map.setStatus({
          dragEnable: true,
          rotateEnable: true,
          pitchEnable: true,
          zoomEnable: true,
          doubleClickZoom: true,
          keyboardEnable: true,
          jogEnable: true,
          scrollWheel: true
        })

        // 触发一次地图重绘
        map.getSize()

        console.log('3D模式：地图状态重置完成')
      } catch (error) {
        console.error('强制重置地图状态失败:', error)
      }
    }

    // 使用高德地图的事件系统
    map.on('mousedown', handleMapMouseDown)
    map.on('mouseup', handleMapMouseUp)
    map.on('dragstart', handleDragStart)
    map.on('dragend', handleDragEnd)

    // 额外的保险措施：监听地图移动结束事件
    map.on('moveend', () => {
      if (rightMousePressed || dragStarted) {
        console.log('3D模式：地图移动结束，重置状态')
        rightMousePressed = false
        dragStarted = false
        forceResetMapState()
      }
    })

    // 清理函数
    const cleanup = () => {
      map.off('mousedown', handleMapMouseDown)
      map.off('mouseup', handleMapMouseUp)
      map.off('dragstart', handleDragStart)
      map.off('dragend', handleDragEnd)
      map.off('moveend')

      if (resetTimer) {
        clearTimeout(resetTimer)
      }
    }

    // 将清理函数存储到地图实例上
    map._mouseInteractionCleanup = cleanup

    console.log('3D鼠标交互bug修复完成（地图事件版本）')

  } catch (error) {
    console.error('修复3D鼠标交互bug失败:', error)
  }
}

// 生命周期钩子
onMounted(async () => {
  // 确保DOM完全渲染后再初始化地图
  await nextTick()
  setTimeout(() => {
    initMap()
  }, 200)
})

onUnmounted(() => {
  destroyMap()
})

/**
 * 调试方法：检查电塔显示状态
 */
const debugTowerStatus = () => {
  console.log('=== 电塔显示状态调试 ===')

  // 3D模型管理器已移除

  if (massMarkerManager.value) {
    console.log('海量点管理器状态:')
    const summary = massMarkerManager.value.getInitSummary()
    console.log('是否已初始化:', summary.isInitialized)
    console.log('海量点数量:', summary.massMarkersCount)
    console.log('标记数量:', summary.markerCounts)
    console.log('渲染错误:', summary.renderError)
  }

  console.log('========================')
}

// 将调试方法暴露到全局，方便在浏览器控制台调用
if (typeof window !== 'undefined') {
  window.debugTowerStatus = debugTowerStatus
}

// 暴露方法供父组件调用
defineExpose({
  initMap,
  destroyMap,
  mapInstance,
  AMapInstance,
  debugTowerStatus
})
</script>

<style scoped>
.yunnan-map-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  overflow: hidden;
  z-index: 1;
}

.map-container {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* 确保高德地图容器样式正确 */
#yunnan-map {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* 高德地图内部元素样式修复 */
:deep(.amap-container) {
  width: 100vw !important;
  height: 100vh !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.amap-maps) {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.amap-layers) {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.amap-layers > div) {
  width: 100vw !important;
  height: 100vh !important;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  border-radius: 8px;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  border-radius: 8px;
}

.error-content {
  text-align: center;
  color: #f56565;
  max-width: 300px;
  padding: 20px;
}

.error-message {
  font-size: 14px;
  color: #666;
  margin: 10px 0;
}

.retry-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.retry-button:hover {
  background-color: #45a049;
}

.retry-button:active {
  transform: translateY(1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .yunnan-map-container {
    height: 100vh;
  }

  .map-container {
    border-radius: 0;
  }

  .loading-overlay,
  .error-overlay {
    border-radius: 0;
  }
}
</style>
